has_custom_menu ||= false
has_custom_pricing ||= false
is_favourite ||= false
wants_filter_data ||= params[:wants_filter_data].present?
for_cache ||= params[:for_cache].present?
scoped_to_category_group = params[:category_group].presence

json.customer_profile_ids supplier.customer_profile_ids
json.has_custom_menu has_custom_menu
json.has_custom_pricing has_custom_pricing
json.is_favourite is_favourite

json.cache! ['supplier-json', supplier.cache_key, wants_filter_data], expires_in: 1.hour do
  json.extract! supplier, :id, :description, :is_featured, :rating, :rating_count, :slug
  json.name supplier.company_name
  json.is_new supplier.is_new?
  json.liquor_license_no supplier.liquor_license_no.presence
  json.abn_acn supplier.abn_acn.presence
  json.image_id  supplier.profile.avatar

  json.tooltip available_features_for(supplier: supplier, for_api: true)

  # Supplier certification flags
  json.is_socially_responsible supplier.is_socially_responsible
  json.is_environmentally_accredited supplier.is_environmentally_accredited
  json.is_registered_charity supplier.is_registered_charity
  json.is_female_owned supplier.is_female_owned
  json.is_lgbtqi_owned supplier.is_lgbtqi_owned
  json.is_rainforest_alliance_certified supplier.is_rainforest_alliance_certified

  if wants_filter_data
    json.dietaries filter_dietaries_for(supplier)
    json.business_codes filter_business_codes_for(supplier)
  end
end

# related to supplier minimums
json.cache! ['supplier-minimums-json', supplier.cache_key, supplier_minimums], expires_in: 1.hour do
  json.min_order supplier_minimums.present? ? format_price(supplier_minimums.minimum_spend) : nil
  json.lead_time supplier_minimums.present? ? supplier_minimums.lead_time : nil
end

# related to scoped category groupe
json.cache! ['supplier-category-group-json', supplier.cache_key, scoped_to_category_group, wants_filter_data], expires_in: 1.hour do
  url_options = { subdomain: 'app' }
  url_options[:category_group] = scoped_to_category_group if scoped_to_category_group.present?
  json.url next_app_supplier_show_url(supplier&.slug, **url_options)
  if wants_filter_data
    json.categories filter_categories_for(supplier: supplier, category_group: scoped_to_category_group).map(&:slug)
  end
end

# related to supplier zone
json.cache! ['supplier-zone-data-json', supplier.cache_key, @suburb.try(:cache_key), wants_filter_data], expires_in: 1.hour do
  supplier_zone = Suppliers::FetchPotentialDeliveryZone.new(supplier: supplier, suburb: @suburb).call
  delivery_fee = supplier_zone.present? ? supplier_zone.delivery_fee : supplier.delivery_fee

  json.delivery_fee format_fee(delivery_fee, for_api: true)
  json.operating_days operating_days_data(supplier: supplier, delivery_zone: supplier_zone)
  json.operating_hours operating_hours(supplier: supplier, delivery_zone: supplier_zone)

  if wants_filter_data
    date_lister = Suppliers::ListClosureDates.new(supplier: supplier, from_date: Time.zone.now, until_date: Time.zone.today + 50.days, delivery_zone: supplier_zone, constrained: true).call
    json.closure_dates date_lister.closure_dates.map{|date| date.strftime('%d/%m/%Y') }
    json.extract! supplier_zone, :operating_hours_start, :operating_hours_end
  end
end

if for_cache
  json.cache! ['supplier-restriction-json', supplier.cache_key], expires_in: 1.hour do
    json.visible_to supplier.customer_profile_ids.presence || nil
  end
end
